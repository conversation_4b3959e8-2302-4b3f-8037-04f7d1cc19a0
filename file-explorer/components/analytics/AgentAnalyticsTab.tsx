// components/analytics/AgentAnalyticsTab.tsx

"use client"

import { useState } from "react"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON>roll<PERSON>rea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { 
  BarChart4, 
  RefreshCw, 
  Download, 
  TrendingUp, 
  TrendingDown, 
  Minus,
  Calendar,
  Filter,
  AlertTriangle,
  CheckCircle,
  Info,
  Lightbulb,
  Loader2
} from "lucide-react"
import { useAgentAnalytics } from "@/hooks/useAgentAnalytics"
import { 
  AnalyticsLineChart, 
  AnalyticsAreaChart, 
  AnalyticsBarChart, 
  AnalyticsPie<PERSON><PERSON>, 
  MetricCard 
} from "./AnalyticsCharts"
import { TIME_PERIODS, METRIC_FORMATS } from "@/types/analytics"
import type { AnalyticsFilter } from "@/types/analytics"

export default function AgentAnalyticsTab() {
  const [selectedPeriod, setSelectedPeriod] = useState<string>("last30Days")
  const [activeTab, setActiveTab] = useState("overview")
  
  const {
    metrics,
    insights,
    recommendations,
    dashboardCards,
    isLoading,
    error,
    filter,
    setFilter,
    refreshAnalytics,
    getChartData,
    getTimeSeriesData,
    formatMetric
  } = useAgentAnalytics()

  // Handle period change
  const handlePeriodChange = (period: string) => {
    setSelectedPeriod(period)
    const newFilter: AnalyticsFilter = {
      ...filter,
      dateRange: TIME_PERIODS[period as keyof typeof TIME_PERIODS]()
    }
    setFilter(newFilter)
  }

  // Handle export
  const handleExport = async () => {
    if (!metrics) return
    
    try {
      const exportData = {
        metrics,
        insights,
        recommendations,
        generatedAt: new Date().toISOString(),
        period: filter.dateRange
      }
      
      const blob = new Blob([JSON.stringify(exportData, null, 2)], { 
        type: 'application/json' 
      })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `agent-analytics-${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Export failed:', error)
    }
  }

  const getInsightIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <AlertTriangle className="h-4 w-4 text-red-500" />
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      case 'positive':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'info':
      default:
        return <Info className="h-4 w-4 text-blue-500" />
    }
  }

  const getChangeIcon = (type: 'increase' | 'decrease' | 'neutral') => {
    switch (type) {
      case 'increase':
        return <TrendingUp className="h-3 w-3 text-green-600" />
      case 'decrease':
        return <TrendingDown className="h-3 w-3 text-red-600" />
      case 'neutral':
      default:
        return <Minus className="h-3 w-3 text-gray-600" />
    }
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center space-y-4">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto" />
          <div>
            <h3 className="text-lg font-medium">Failed to load analytics</h3>
            <p className="text-muted-foreground">{error}</p>
          </div>
          <Button onClick={refreshAnalytics}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <div className="flex items-center gap-2">
          <BarChart4 className="h-5 w-5 text-muted-foreground" />
          <h2 className="text-lg font-semibold">Analytics</h2>
          {metrics && (
            <Badge variant="outline" className="text-xs">
              {metrics.totalTasks} tasks analyzed
            </Badge>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          <Select value={selectedPeriod} onValueChange={handlePeriodChange}>
            <SelectTrigger className="w-40">
              <Calendar className="h-4 w-4 mr-2" />
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="today">Today</SelectItem>
              <SelectItem value="last7Days">Last 7 Days</SelectItem>
              <SelectItem value="last30Days">Last 30 Days</SelectItem>
              <SelectItem value="thisWeek">This Week</SelectItem>
              <SelectItem value="thisMonth">This Month</SelectItem>
            </SelectContent>
          </Select>
          
          <Button variant="outline" size="sm" onClick={handleExport}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          
          <Button variant="outline" size="sm" onClick={refreshAnalytics}>
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        {isLoading ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center space-y-4">
              <Loader2 className="h-8 w-8 animate-spin mx-auto text-muted-foreground" />
              <p className="text-muted-foreground">Loading analytics...</p>
            </div>
          </div>
        ) : !metrics ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center space-y-4">
              <BarChart4 className="h-12 w-12 text-muted-foreground mx-auto" />
              <div>
                <h3 className="text-lg font-medium">No analytics data</h3>
                <p className="text-muted-foreground">
                  Analytics will appear here once agents start executing tasks.
                </p>
              </div>
            </div>
          </div>
        ) : (
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
            <TabsList className="mx-4 mt-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="performance">Performance</TabsTrigger>
              <TabsTrigger value="costs">Costs</TabsTrigger>
              <TabsTrigger value="insights">Insights</TabsTrigger>
            </TabsList>
            
            <div className="flex-1 overflow-hidden">
              <TabsContent value="overview" className="h-full m-0">
                <ScrollArea className="h-full">
                  <div className="p-4 space-y-6">
                    {/* Metric Cards */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {dashboardCards.map((card) => (
                        <MetricCard
                          key={card.id}
                          title={card.title}
                          value={card.format ? formatMetric(Number(card.value), card.format) : card.value}
                          change={card.change}
                          icon={card.icon}
                          color={card.color}
                          description={card.description}
                        />
                      ))}
                    </div>

                    {/* Charts */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      <AnalyticsLineChart
                        title="Daily Task Trend"
                        description="Tasks completed over time"
                        data={getTimeSeriesData('daily-tasks')}
                        height={250}
                      />
                      
                      <AnalyticsPieChart
                        title="Agent Distribution"
                        description="Task distribution by agent type"
                        data={getChartData('agent-distribution')}
                        height={250}
                      />
                      
                      <AnalyticsBarChart
                        title="Model Usage"
                        description="API calls by model"
                        data={getChartData('model-usage')}
                        height={250}
                      />
                      
                      <AnalyticsAreaChart
                        title="Weekly Token Usage"
                        description="Token consumption over time"
                        data={getTimeSeriesData('weekly-tokens')}
                        height={250}
                      />
                    </div>
                  </div>
                </ScrollArea>
              </TabsContent>

              <TabsContent value="performance" className="h-full m-0">
                <ScrollArea className="h-full">
                  <div className="p-4 space-y-6">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-sm font-medium">Agent Success Rates</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-3">
                            {Object.entries(metrics.agentSuccessRates).map(([agent, rate]) => (
                              <div key={agent} className="flex items-center justify-between">
                                <span className="text-sm">{agent}</span>
                                <div className="flex items-center gap-2">
                                  <div className="w-20 bg-gray-200 rounded-full h-2">
                                    <div 
                                      className="bg-green-500 h-2 rounded-full" 
                                      style={{ width: `${rate}%` }}
                                    />
                                  </div>
                                  <span className="text-sm font-medium">{rate.toFixed(1)}%</span>
                                </div>
                              </div>
                            ))}
                          </div>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardHeader>
                          <CardTitle className="text-sm font-medium">Average Response Times</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-3">
                            {Object.entries(metrics.agentAverageTime).map(([agent, time]) => (
                              <div key={agent} className="flex items-center justify-between">
                                <span className="text-sm">{agent}</span>
                                <span className="text-sm font-medium">
                                  {formatMetric(time, 'duration')}
                                </span>
                              </div>
                            ))}
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </div>
                </ScrollArea>
              </TabsContent>

              <TabsContent value="costs" className="h-full m-0">
                <ScrollArea className="h-full">
                  <div className="p-4 space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <MetricCard
                        title="Today's Cost"
                        value={formatMetric(metrics.costToday, 'currency')}
                        icon="💰"
                        color="green"
                      />
                      <MetricCard
                        title="This Week"
                        value={formatMetric(metrics.costThisWeek, 'currency')}
                        icon="📊"
                        color="blue"
                      />
                      <MetricCard
                        title="This Month"
                        value={formatMetric(metrics.costThisMonth, 'currency')}
                        icon="📈"
                        color="purple"
                      />
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      <AnalyticsPieChart
                        title="Cost Breakdown by Agent"
                        description="Spending distribution across agents"
                        data={getChartData('cost-breakdown')}
                        height={300}
                      />
                      
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-sm font-medium">Model Cost Analysis</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-3">
                            {Object.entries(metrics.modelUsageBreakdown).map(([model, data]) => (
                              <div key={model} className="space-y-1">
                                <div className="flex items-center justify-between">
                                  <span className="text-sm font-medium">{model}</span>
                                  <span className="text-sm">{formatMetric(data.cost, 'currency')}</span>
                                </div>
                                <div className="text-xs text-muted-foreground">
                                  {data.calls} calls • {data.tokens.toLocaleString()} tokens
                                </div>
                              </div>
                            ))}
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </div>
                </ScrollArea>
              </TabsContent>

              <TabsContent value="insights" className="h-full m-0">
                <ScrollArea className="h-full">
                  <div className="p-4 space-y-6">
                    {/* Insights */}
                    {insights.length > 0 && (
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-sm font-medium flex items-center gap-2">
                            <Info className="h-4 w-4" />
                            Insights
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-3">
                            {insights.map((insight) => (
                              <div key={insight.id} className="flex items-start gap-3 p-3 rounded-lg border">
                                {getInsightIcon(insight.severity)}
                                <div className="flex-1 space-y-1">
                                  <h4 className="text-sm font-medium">{insight.title}</h4>
                                  <p className="text-sm text-muted-foreground">{insight.description}</p>
                                  <div className="flex items-center gap-2">
                                    <Badge variant="outline" className="text-xs">
                                      {insight.type}
                                    </Badge>
                                    <Badge variant="outline" className="text-xs">
                                      {insight.impact} impact
                                    </Badge>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </CardContent>
                      </Card>
                    )}

                    {/* Recommendations */}
                    {recommendations.length > 0 && (
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-sm font-medium flex items-center gap-2">
                            <Lightbulb className="h-4 w-4" />
                            Recommendations
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-4">
                            {recommendations.map((rec) => (
                              <div key={rec.id} className="p-4 rounded-lg border space-y-3">
                                <div className="flex items-start justify-between">
                                  <div>
                                    <h4 className="text-sm font-medium">{rec.title}</h4>
                                    <p className="text-sm text-muted-foreground mt-1">{rec.description}</p>
                                  </div>
                                  <Badge variant="outline" className="text-xs">
                                    {rec.priority}
                                  </Badge>
                                </div>
                                
                                <div className="text-sm">
                                  <div className="font-medium text-green-600 mb-2">
                                    Expected Impact: {rec.estimatedImpact}
                                  </div>
                                  <div className="space-y-1">
                                    <div className="flex items-center gap-2 text-muted-foreground">
                                      <span>Difficulty:</span>
                                      <Badge variant="outline" className="text-xs">
                                        {rec.implementation.difficulty}
                                      </Badge>
                                    </div>
                                    <div className="text-muted-foreground">
                                      Time Required: {rec.implementation.timeRequired}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </CardContent>
                      </Card>
                    )}

                    {insights.length === 0 && recommendations.length === 0 && (
                      <div className="text-center py-8">
                        <Lightbulb className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                        <h3 className="text-lg font-medium">No insights available</h3>
                        <p className="text-muted-foreground">
                          Insights and recommendations will appear as more data is collected.
                        </p>
                      </div>
                    )}
                  </div>
                </ScrollArea>
              </TabsContent>
            </div>
          </Tabs>
        )}
      </div>
    </div>
  )
}
