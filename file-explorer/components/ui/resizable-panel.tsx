"use client"

import { useState, useRef, useEffect, ReactNode } from "react"
import { cn } from "@/lib/utils"

interface ResizablePanelProps {
  children: ReactNode
  width: number
  onWidthChange: (width: number) => void
  minWidth?: number
  maxWidth?: number
  className?: string
  resizeHandle?: "left" | "right"
}

export default function ResizablePanel({
  children,
  width,
  onWidthChange,
  minWidth = 300,
  maxWidth = 600,
  className,
  resizeHandle = "left"
}: ResizablePanelProps) {
  const [isResizing, setIsResizing] = useState(false)
  const panelRef = useRef<HTMLDivElement>(null)
  const startXRef = useRef(0)
  const startWidthRef = useRef(0)

  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault()
    setIsResizing(true)
    startXRef.current = e.clientX
    startWidthRef.current = width
    
    // Add global mouse event listeners
    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
    document.body.style.cursor = 'col-resize'
    document.body.style.userSelect = 'none'
  }

  const handleMouseMove = (e: MouseEvent) => {
    if (!isResizing) return

    const deltaX = resizeHandle === "left" 
      ? startXRef.current - e.clientX 
      : e.clientX - startXRef.current
    
    const newWidth = startWidthRef.current + deltaX
    const clampedWidth = Math.max(minWidth, Math.min(maxWidth, newWidth))
    
    onWidthChange(clampedWidth)
  }

  const handleMouseUp = () => {
    setIsResizing(false)
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
    document.body.style.cursor = ''
    document.body.style.userSelect = ''
  }

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
      document.body.style.cursor = ''
      document.body.style.userSelect = ''
    }
  }, [])

  return (
    <div
      ref={panelRef}
      className={cn(
        "relative border-l border-editor-border bg-background/95 backdrop-blur-sm transition-all duration-200 ease-in-out",
        isResizing && "transition-none",
        className
      )}
      style={{ width: `${width}px` }}
    >
      {/* Resize handle */}
      <div
        className={cn(
          "absolute top-0 bottom-0 w-1 cursor-col-resize hover:bg-editor-highlight/50 transition-colors group z-10",
          resizeHandle === "left" ? "left-0" : "right-0"
        )}
        onMouseDown={handleMouseDown}
      >
        <div 
          className={cn(
            "absolute top-1/2 -translate-y-1/2 w-1 h-8 bg-editor-highlight/30 rounded opacity-0 group-hover:opacity-100 transition-opacity",
            resizeHandle === "left" ? "left-0 rounded-r" : "right-0 rounded-l"
          )}
        />
      </div>

      {/* Panel content */}
      <div className="h-full overflow-hidden">
        {children}
      </div>
    </div>
  )
}
