// components/agents/openai-models.ts

export interface OpenAIModelMetadata {
  id: string;
  label: string;
  description?: string;
  contextSize?: number;
  pricing?: {
    input: number;  // per 1K tokens
    output: number; // per 1K tokens
  };
  tags?: string[];
  releaseDate?: string;
}

/**
 * Verified OpenAI model metadata from official sources
 * Only includes models with confirmed specifications
 */
export const OPENAI_MODEL_METADATA: Record<string, OpenAIModelMetadata> = {
  'gpt-4': {
    id: 'gpt-4',
    label: 'GPT-4',
    description: 'Most capable GPT-4 model with superior reasoning and complex task handling',
    contextSize: 8192,
    pricing: {
      input: 0.03,
      output: 0.06
    },
    tags: ['advanced-reasoning', 'general-purpose', 'creative-writing', 'complex-analysis'],
    releaseDate: '2023-03-14'
  },
  'gpt-4-turbo': {
    id: 'gpt-4-turbo',
    label: 'GPT-4 Turbo',
    description: 'Faster and more efficient GPT-4 with extended context window',
    contextSize: 128000,
    pricing: {
      input: 0.01,
      output: 0.03
    },
    tags: ['fast', 'high-context', 'advanced-reasoning', 'multimodal'],
    releaseDate: '2023-11-06'
  },
  'gpt-4-turbo-preview': {
    id: 'gpt-4-turbo-preview',
    label: 'GPT-4 Turbo Preview',
    description: 'Preview version of GPT-4 Turbo with latest improvements',
    contextSize: 128000,
    pricing: {
      input: 0.01,
      output: 0.03
    },
    tags: ['preview', 'fast', 'high-context', 'advanced-reasoning'],
    releaseDate: '2023-11-06'
  },
  'gpt-4o': {
    id: 'gpt-4o',
    label: 'GPT-4o',
    description: 'Flagship multimodal model with vision, audio, and text capabilities',
    contextSize: 128000,
    pricing: {
      input: 0.005,
      output: 0.02
    },
    tags: ['multimodal', 'vision', 'audio', 'fast', 'high-context'],
    releaseDate: '2024-05-13'
  },
  'gpt-4o-mini': {
    id: 'gpt-4o-mini',
    label: 'GPT-4o Mini',
    description: 'Affordable and intelligent small model for fast, lightweight tasks',
    contextSize: 128000,
    pricing: {
      input: 0.0006,
      output: 0.0024
    },
    tags: ['affordable', 'fast', 'cost-effective', 'high-context'],
    releaseDate: '2024-07-18'
  },
  'gpt-4o-mini-audio-preview-2024-12-17': {
    id: 'gpt-4o-mini-audio-preview-2024-12-17',
    label: 'GPT-4o Mini Audio Preview',
    description: 'Preview model with audio and text capabilities for realtime applications',
    contextSize: 128000,
    pricing: {
      input: 0.0006,
      output: 0.0024
    },
    tags: ['preview', 'audio', 'realtime', 'multimodal', 'affordable'],
    releaseDate: '2024-12-17'
  },
  'gpt-4o-audio-preview': {
    id: 'gpt-4o-audio-preview',
    label: 'GPT-4o Audio Preview',
    description: 'Preview model with advanced audio and text capabilities',
    contextSize: 128000,
    pricing: {
      input: 0.005,
      output: 0.02
    },
    tags: ['preview', 'audio', 'realtime', 'multimodal', 'advanced'],
    releaseDate: '2024-10-01'
  },
  'gpt-4o-realtime-preview': {
    id: 'gpt-4o-realtime-preview',
    label: 'GPT-4o Realtime Preview',
    description: 'Preview model optimized for realtime audio and text interactions',
    contextSize: 128000,
    pricing: {
      input: 0.005,
      output: 0.02
    },
    tags: ['preview', 'realtime', 'audio', 'multimodal', 'fast'],
    releaseDate: '2024-10-01'
  },
  'gpt-3.5-turbo': {
    id: 'gpt-3.5-turbo',
    label: 'GPT-3.5 Turbo',
    description: 'Fast, inexpensive model for simple tasks',
    contextSize: 16385,
    pricing: {
      input: 0.0005,
      output: 0.0015
    },
    tags: ['affordable', 'fast', 'chat', 'legacy'],
    releaseDate: '2023-03-01'
  }
};

/**
 * Get metadata for a specific OpenAI model
 */
export function getOpenAIModelMetadata(modelId: string): OpenAIModelMetadata | null {
  return OPENAI_MODEL_METADATA[modelId] || null;
}

/**
 * Check if a model has verified metadata
 */
export function hasOpenAIModelMetadata(modelId: string): boolean {
  return modelId in OPENAI_MODEL_METADATA;
}

/**
 * Get all available OpenAI models with metadata
 */
export function getOpenAIModelsWithMetadata(): OpenAIModelMetadata[] {
  return Object.values(OPENAI_MODEL_METADATA);
}

/**
 * Get model series for grouping
 */
export function getOpenAIModelSeries(modelId: string): string {
  if (modelId.startsWith('gpt-4o')) {
    return 'GPT-4o';
  }
  if (modelId.startsWith('gpt-4')) {
    return 'GPT-4';
  }
  if (modelId.startsWith('gpt-3.5')) {
    return 'GPT-3.5';
  }
  return 'Other';
}
