"use client"

import { useState, useEffect } from "react"
import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"
import { Wifi, WifiOff } from "lucide-react"

interface SyncStatusIndicatorProps {
  className?: string
}

export default function SyncStatusIndicator({ className }: SyncStatusIndicatorProps) {
  const [isConnected, setIsConnected] = useState(false)

  useEffect(() => {
    // Check if we're in an Electron environment with IPC capabilities
    const hasIPC = typeof window !== 'undefined' && window.electronAPI
    setIsConnected(hasIPC)
  }, [])

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <Badge
        variant="outline"
        className={cn(
          "text-xs flex items-center gap-1 transition-colors",
          isConnected
            ? "bg-green-500/10 text-green-600 border-green-500/20"
            : "bg-red-500/10 text-red-600 border-red-500/20"
        )}
      >
        {isConnected ? (
          <>
            <Wifi className="h-3 w-3" />
            Synced
          </>
        ) : (
          <>
            <WifiOff className="h-3 w-3" />
            Offline
          </>
        )}
      </Badge>
    </div>
  )
}
