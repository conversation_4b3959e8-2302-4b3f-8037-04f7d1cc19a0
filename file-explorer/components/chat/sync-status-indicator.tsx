"use client"

import { useState, useEffect } from "react"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { Wifi, WifiOff } from "lucide-react"

interface SyncStatusIndicatorProps {
  className?: string
}

export default function SyncStatusIndicator({ className }: SyncStatusIndicatorProps) {
  const [isConnected, setIsConnected] = useState(true)
  const [lastSyncTime, setLastSyncTime] = useState<Date>(new Date())

  // Check connection status
  useEffect(() => {
    const checkConnection = () => {
      // Check if we're in an Electron environment and have IPC access
      const hasElectronAPI = typeof window !== 'undefined' && window.electronAPI
      const hasIPC = hasElectronAPI && window.electronAPI.ipc
      
      setIsConnected(hasIPC)
      
      if (hasIPC) {
        setLastSyncTime(new Date())
      }
    }

    // Initial check
    checkConnection()

    // Set up periodic connection checks
    const interval = setInterval(checkConnection, 5000) // Check every 5 seconds

    // Listen for online/offline events
    const handleOnline = () => {
      checkConnection()
    }
    
    const handleOffline = () => {
      setIsConnected(false)
    }

    if (typeof window !== 'undefined') {
      window.addEventListener('online', handleOnline)
      window.addEventListener('offline', handleOffline)
    }

    return () => {
      clearInterval(interval)
      if (typeof window !== 'undefined') {
        window.removeEventListener('online', handleOnline)
        window.removeEventListener('offline', handleOffline)
      }
    }
  }, [])

  const formatLastSync = () => {
    const now = new Date()
    const diffMs = now.getTime() - lastSyncTime.getTime()
    const diffSeconds = Math.floor(diffMs / 1000)
    
    if (diffSeconds < 60) {
      return 'Just now'
    } else if (diffSeconds < 3600) {
      const minutes = Math.floor(diffSeconds / 60)
      return `${minutes}m ago`
    } else {
      return lastSyncTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    }
  }

  return (
    <Badge 
      variant="outline" 
      className={cn(
        "text-xs flex items-center gap-1 transition-colors",
        isConnected 
          ? "bg-green-500/10 text-green-600 border-green-500/20" 
          : "bg-red-500/10 text-red-600 border-red-500/20",
        className
      )}
      title={isConnected ? `Last sync: ${formatLastSync()}` : "Disconnected from main window"}
    >
      <div className={cn(
        "w-2 h-2 rounded-full",
        isConnected ? "bg-green-500 animate-pulse" : "bg-red-500"
      )} />
      {isConnected ? (
        <>
          <Wifi className="h-3 w-3" />
          Synced
        </>
      ) : (
        <>
          <WifiOff className="h-3 w-3" />
          Offline
        </>
      )}
    </Badge>
  )
}
