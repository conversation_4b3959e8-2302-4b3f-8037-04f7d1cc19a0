// components/settings/api-keys-settings.tsx
import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Trash2, ExternalLink, Info, Check, X, Copy } from 'lucide-react';
import { LLMProvider, getAllProviders, getProviderConfig, getProviderModels } from '../agents/llm-provider-registry';
import { LLMRequestService } from '../agents/llm-request-service';
import { ModelRegistryService } from '../agents/model-registry-service';
import { AnthropicModelSelector } from '../agents/anthropic-model-selector';
import { getAnthropicModelOptions } from '../agents/anthropic-models';
import { OpenAIModelSelector } from '../agents/openai-model-selector';
import { UniversalModelSelector } from '../agents/universal-model-selector';
import { getOpenRouterModelMetadata, hasOpenRouterModelMetadata, getOpenRouterModelProvider } from '../agents/openrouter-models';
import { getGoogleModelMetadata, hasGoogleModelMetadata, getGoogleModelSeries } from '../agents/google-models';
import { getDeepSeekModelMetadata, hasDeepSeekModelMetadata, getDeepSeekModelSeries } from '../agents/deepseek-models';
import { getFireworksModelMetadata, hasFireworksModelMetadata, getFireworksModelProvider } from '../agents/fireworks-models';
import { SettingsManager, AllSettings } from './settings-manager';

interface ApiKeysSettingsProps {
  settingsManager: SettingsManager;
  settings: AllSettings;
}

interface ProviderState {
  apiKey: string;
  selectedModel: string;
  isValidating: boolean;
  isValid: boolean | null;
  customModel: string;
  availableModels: string[];
  isLoadingModels: boolean;
  modelsLastFetched: number | null;
}

export const ApiKeysSettings: React.FC<ApiKeysSettingsProps> = ({ settingsManager, settings }) => {
  const [providerStates, setProviderStates] = useState<Record<LLMProvider, ProviderState>>({} as Record<LLMProvider, ProviderState>);
  const [showKeys, setShowKeys] = useState(false);
  const llmService = LLMRequestService.getInstance();
  const modelRegistry = ModelRegistryService.getInstance();

  // Initialize provider states
  useEffect(() => {
    const initialStates: Record<LLMProvider, ProviderState> = {} as Record<LLMProvider, ProviderState>;

    getAllProviders().forEach(provider => {
      const config = getProviderConfig(provider);
      let staticModels = getProviderModels(provider);

      // Use enhanced Anthropic models list
      if (provider === 'anthropic') {
        staticModels = getAnthropicModelOptions().map(option => option.value);
      }

      initialStates[provider] = {
        apiKey: settings.apiKeys[provider] || '',
        selectedModel: staticModels[0] || 'default',
        isValidating: false,
        isValid: null,
        customModel: '',
        availableModels: staticModels,
        isLoadingModels: false,
        modelsLastFetched: null
      };
    });

    setProviderStates(initialStates);
  }, [settings.apiKeys]);

  const handleApiKeyChange = (provider: LLMProvider, value: string) => {
    setProviderStates(prev => ({
      ...prev,
      [provider]: {
        ...prev[provider],
        apiKey: value,
        isValid: null // Reset validation state
      }
    }));

    if (value.trim()) {
      settingsManager.setApiKey(provider, value);
      llmService.setApiKey(provider, value);
    } else {
      settingsManager.removeApiKey(provider);
    }
  };

  const handleModelChange = (provider: LLMProvider, model: string) => {
    setProviderStates(prev => ({
      ...prev,
      [provider]: {
        ...prev[provider],
        selectedModel: model
      }
    }));
  };

  const handleCustomModelChange = (provider: LLMProvider, customModel: string) => {
    setProviderStates(prev => ({
      ...prev,
      [provider]: {
        ...prev[provider],
        customModel
      }
    }));
  };

  const validateApiKey = async (provider: LLMProvider) => {
    const state = providerStates[provider];
    if (!state.apiKey.trim()) return;

    setProviderStates(prev => ({
      ...prev,
      [provider]: {
        ...prev[provider],
        isValidating: true
      }
    }));

    try {
      const isValid = await llmService.validateApiKey(provider, state.apiKey);
      setProviderStates(prev => ({
        ...prev,
        [provider]: {
          ...prev[provider],
          isValidating: false,
          isValid
        }
      }));

      // If validation successful and provider supports model fetching, fetch models
      if (isValid) {
        const config = getProviderConfig(provider);
        if (config.supportsModelFetching && !state.modelsLastFetched && isElectronAPIAvailable()) {
          setTimeout(() => fetchModelsForProvider(provider), 500); // Small delay for UX
        }
      }
    } catch (error) {
      console.error('API key validation failed:', error);
      setProviderStates(prev => ({
        ...prev,
        [provider]: {
          ...prev[provider],
          isValidating: false,
          isValid: false
        }
      }));
    }
  };

  const deleteProvider = (provider: LLMProvider) => {
    settingsManager.removeApiKey(provider);
    setProviderStates(prev => ({
      ...prev,
      [provider]: {
        ...prev[provider],
        apiKey: '',
        isValid: null
      }
    }));
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const openDocumentation = (provider: LLMProvider) => {
    const config = getProviderConfig(provider);
    window.open(config.documentationUrl, '_blank');
  };

  const isElectronAPIAvailable = (): boolean => {
    return typeof window !== 'undefined' &&
           window.electronAPI?.llm?.fetchModels !== undefined;
  };

  const fetchModelsForProvider = async (provider: LLMProvider) => {
    const state = providerStates[provider];
    if (!state.apiKey.trim()) {
      console.log(`No API key for ${provider}, skipping model fetch`);
      return;
    }

    const config = getProviderConfig(provider);
    if (!config.supportsModelFetching) {
      console.log(`${provider} doesn't support dynamic model fetching`);
      return;
    }

    // Check if Electron API is available
    if (!isElectronAPIAvailable()) {
      console.log(`Electron API not available, cannot fetch models for ${provider}`);
      return;
    }

    setProviderStates(prev => ({
      ...prev,
      [provider]: {
        ...prev[provider],
        isLoadingModels: true
      }
    }));

    try {
      console.log(`Fetching models for ${provider}...`);
      const models = await modelRegistry.fetchModels(provider, state.apiKey);

      setProviderStates(prev => ({
        ...prev,
        [provider]: {
          ...prev[provider],
          availableModels: models,
          isLoadingModels: false,
          modelsLastFetched: Date.now()
        }
      }));

      console.log(`Successfully fetched ${models.length} models for ${provider}`);
    } catch (error) {
      console.error(`Failed to fetch models for ${provider}:`, error);

      setProviderStates(prev => ({
        ...prev,
        [provider]: {
          ...prev[provider],
          isLoadingModels: false
        }
      }));
    }
  };

  const refreshModels = (provider: LLMProvider) => {
    fetchModelsForProvider(provider);
  };

  const getValidationIcon = (state: ProviderState) => {
    if (state.isValidating) {
      return <div className="animate-spin h-4 w-4 border-2 border-gray-300 border-t-blue-600 rounded-full" />;
    }
    if (state.isValid === true) {
      return <Check className="h-4 w-4 text-green-500" />;
    }
    if (state.isValid === false) {
      return <X className="h-4 w-4 text-red-500" />;
    }
    return null;
  };

  // Helper function to get metadata functions for each provider
  const getProviderMetadataFunctions = (provider: LLMProvider) => {
    switch (provider) {
      case 'openrouter':
        return {
          getMetadata: getOpenRouterModelMetadata,
          hasMetadata: hasOpenRouterModelMetadata,
          getSeries: getOpenRouterModelProvider
        };
      case 'google':
        return {
          getMetadata: getGoogleModelMetadata,
          hasMetadata: hasGoogleModelMetadata,
          getSeries: getGoogleModelSeries
        };
      case 'deepseek':
        return {
          getMetadata: getDeepSeekModelMetadata,
          hasMetadata: hasDeepSeekModelMetadata,
          getSeries: getDeepSeekModelSeries
        };
      case 'fireworks':
        return {
          getMetadata: getFireworksModelMetadata,
          hasMetadata: hasFireworksModelMetadata,
          getSeries: getFireworksModelProvider
        };
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      {getAllProviders().map((provider) => {
        const config = getProviderConfig(provider);
        const state = providerStates[provider];

        if (!state) return null;

        return (
          <Card key={provider} className="relative">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <CardTitle className="text-lg">{config.name} API Key</CardTitle>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => openDocumentation(provider)}
                    className="h-6 w-6 p-0"
                  >
                    <Info className="h-4 w-4" />
                  </Button>
                  {getValidationIcon(state)}
                </div>
                <div className="flex items-center gap-2">
                  {state.apiKey && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => validateApiKey(provider)}
                      disabled={state.isValidating}
                    >
                      Validate
                    </Button>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Input
                    type={showKeys ? 'text' : 'password'}
                    placeholder={`Enter ${config.name} API key`}
                    value={state.apiKey}
                    onChange={(e) => handleApiKeyChange(provider, e.target.value)}
                    className="flex-1"
                  />
                  {state.apiKey && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(state.apiKey)}
                      className="h-9 w-9 p-0"
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label>Custom Model</Label>
                <Input
                  placeholder="Enter a compatible model name"
                  value={state.customModel}
                  onChange={(e) => handleCustomModelChange(provider, e.target.value)}
                />
              </div>

              {/* Model Selection - Use specialized components for providers with metadata */}
              {provider === 'anthropic' ? (
                <AnthropicModelSelector
                  value={state.selectedModel}
                  onChange={(value) => handleModelChange(provider, value)}
                  disabled={state.isLoadingModels}
                  placeholder="Select an Anthropic model"
                />
              ) : provider === 'openai' ? (
                <OpenAIModelSelector
                  value={state.selectedModel}
                  onChange={(value) => handleModelChange(provider, value)}
                  apiKey={state.apiKey}
                  availableModels={state.availableModels}
                  isLoadingModels={state.isLoadingModels}
                  onRefreshModels={() => refreshModels(provider)}
                  disabled={state.isLoadingModels}
                  placeholder="Select an OpenAI model"
                />
              ) : getProviderMetadataFunctions(provider) ? (
                <UniversalModelSelector
                  value={state.selectedModel}
                  onChange={(value) => handleModelChange(provider, value)}
                  availableModels={state.availableModels}
                  getModelMetadata={getProviderMetadataFunctions(provider)!.getMetadata}
                  hasModelMetadata={getProviderMetadataFunctions(provider)!.hasMetadata}
                  getModelSeries={getProviderMetadataFunctions(provider)!.getSeries}
                  isLoadingModels={state.isLoadingModels}
                  onRefreshModels={() => refreshModels(provider)}
                  apiKey={state.apiKey}
                  showRefresh={config.supportsModelFetching}
                  disabled={state.isLoadingModels}
                  placeholder={`Select a ${config.name} model`}
                  providerName={config.name}
                />
              ) : (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label>Model</Label>
                    <div className="flex items-center gap-2">
                      {config.supportsModelFetching && (
                        <>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => refreshModels(provider)}
                            disabled={state.isLoadingModels || !state.apiKey || !isElectronAPIAvailable()}
                            className="h-6 px-2 text-xs"
                          >
                            {state.isLoadingModels ? (
                              <div className="animate-spin h-3 w-3 border border-gray-300 border-t-blue-600 rounded-full" />
                            ) : (
                              'Refresh'
                            )}
                          </Button>
                          {!isElectronAPIAvailable() && (
                            <span className="text-xs text-orange-600 dark:text-orange-400">
                              Electron required
                            </span>
                          )}
                          {state.modelsLastFetched && (
                            <span className="text-xs text-muted-foreground">
                              {Math.round((Date.now() - state.modelsLastFetched) / 60000)}m ago
                            </span>
                          )}
                        </>
                      )}
                    </div>
                  </div>
                  <Select
                    value={state.selectedModel}
                    onValueChange={(value) => handleModelChange(provider, value)}
                    onOpenChange={(open) => {
                      if (open && config.supportsModelFetching && state.apiKey && !state.modelsLastFetched && isElectronAPIAvailable()) {
                        fetchModelsForProvider(provider);
                      }
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={state.isLoadingModels ? "Loading models..." : "Select a model"} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="None">None</SelectItem>
                      {state.availableModels.map((model) => (
                        <SelectItem key={model} value={model}>
                          {model}
                        </SelectItem>
                      ))}
                      {config.supportsModelFetching && state.availableModels.length > getProviderModels(provider).length && (
                        <div className="px-2 py-1 text-xs text-muted-foreground border-t">
                          {state.availableModels.length - getProviderModels(provider).length} additional models loaded
                        </div>
                      )}
                    </SelectContent>
                  </Select>
                </div>
              )}

              <div className="flex justify-between">
                <Button
                  variant="outline"
                  onClick={() => validateApiKey(provider)}
                  disabled={!state.apiKey || state.isValidating}
                >
                  Change
                </Button>
                <Button
                  variant="destructive"
                  onClick={() => deleteProvider(provider)}
                  disabled={!state.apiKey}
                >
                  Delete
                </Button>
              </div>

              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Badge variant="secondary">
                  ${config.costPer1kTokens.input.toFixed(4)}/1K input tokens
                </Badge>
                <Badge variant="secondary">
                  ${config.costPer1kTokens.output.toFixed(4)}/1K output tokens
                </Badge>
              </div>
            </CardContent>
          </Card>
        );
      })}

      <div className="flex justify-between items-center pt-4">
        <Button
          variant="outline"
          onClick={() => setShowKeys(!showKeys)}
        >
          {showKeys ? 'Hide' : 'Show'} API Keys
        </Button>
        <div className="text-sm text-muted-foreground">
          API keys are stored securely and encrypted
        </div>
      </div>
    </div>
  );
};

export default ApiKeysSettings;
