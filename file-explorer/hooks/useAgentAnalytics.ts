// hooks/useAgentAnalytics.ts

import { useState, useEffect, useCallback } from "react"
import { getAnalyticsService } from "@/services/analytics-service"
import type {
  AgentAnalyticsMetrics,
  AnalyticsFilter,
  AnalyticsInsight,
  AnalyticsRecommendation,
  AnalyticsDashboardCard,
  ChartDataPoint,
  TimeSeriesDataPoint
} from "@/types/analytics"
import { METRIC_FORMATS, TIME_PERIODS } from "@/types/analytics"

export interface UseAgentAnalyticsReturn {
  // Data
  metrics: AgentAnalyticsMetrics | null
  insights: AnalyticsInsight[]
  recommendations: AnalyticsRecommendation[]
  dashboardCards: AnalyticsDashboardCard[]
  isLoading: boolean
  error: string | null
  
  // Filtering
  filter: AnalyticsFilter
  setFilter: (filter: AnalyticsFilter) => void
  clearFilter: () => void
  
  // Actions
  refreshAnalytics: () => Promise<void>
  
  // Chart data helpers
  getChartData: (type: 'agent-distribution' | 'task-trend' | 'cost-breakdown' | 'model-usage') => ChartDataPoint[]
  getTimeSeriesData: (type: 'daily-tasks' | 'weekly-tokens' | 'monthly-usage') => TimeSeriesDataPoint[]
  
  // Utility
  formatMetric: (value: number, format: keyof typeof METRIC_FORMATS) => string
  getMetricChange: (current: number, previous: number) => { value: number; type: 'increase' | 'decrease' | 'neutral' }
}

const defaultFilter: AnalyticsFilter = {
  dateRange: TIME_PERIODS.last30Days()
}

export function useAgentAnalytics(): UseAgentAnalyticsReturn {
  const [metrics, setMetrics] = useState<AgentAnalyticsMetrics | null>(null)
  const [insights, setInsights] = useState<AnalyticsInsight[]>([])
  const [recommendations, setRecommendations] = useState<AnalyticsRecommendation[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [filter, setFilter] = useState<AnalyticsFilter>(defaultFilter)
  const [analyticsService] = useState(() => getAnalyticsService())

  // Load analytics data
  const loadAnalytics = useCallback(async () => {
    try {
      setIsLoading(true)
      setError(null)
      
      const [analyticsMetrics, analyticsInsights, analyticsRecommendations] = await Promise.all([
        analyticsService.getAnalyticsMetrics(filter),
        analyticsService.generateInsights(metrics || {} as AgentAnalyticsMetrics),
        analyticsService.generateRecommendations(metrics || {} as AgentAnalyticsMetrics)
      ])
      
      setMetrics(analyticsMetrics)
      setInsights(analyticsInsights)
      setRecommendations(analyticsRecommendations)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load analytics'
      setError(errorMessage)
      console.error('Failed to load analytics:', err)
    } finally {
      setIsLoading(false)
    }
  }, [analyticsService, filter, metrics])

  // Load analytics on mount and when filter changes
  useEffect(() => {
    loadAnalytics()
  }, [loadAnalytics])

  // Refresh analytics
  const refreshAnalytics = useCallback(async () => {
    await loadAnalytics()
  }, [loadAnalytics])

  // Clear filter
  const clearFilter = useCallback(() => {
    setFilter(defaultFilter)
  }, [])

  // Generate dashboard cards
  const dashboardCards: AnalyticsDashboardCard[] = metrics ? [
    {
      id: 'total-tasks',
      title: 'Total Tasks',
      value: metrics.totalTasks,
      change: {
        value: metrics.tasksToday,
        type: metrics.tasksToday > 0 ? 'increase' : 'neutral',
        period: 'today'
      },
      icon: '📋',
      color: 'blue',
      format: 'number'
    },
    {
      id: 'success-rate',
      title: 'Success Rate',
      value: metrics.totalTasks > 0 ? (metrics.completedTasks / metrics.totalTasks) * 100 : 0,
      change: {
        value: 0, // Would need historical data
        type: 'neutral',
        period: 'vs last period'
      },
      icon: '✅',
      color: 'green',
      format: 'percentage'
    },
    {
      id: 'avg-completion-time',
      title: 'Avg Completion Time',
      value: metrics.averageCompletionTime,
      change: {
        value: 0, // Would need historical data
        type: 'neutral',
        period: 'vs last period'
      },
      icon: '⏱️',
      color: 'orange',
      format: 'duration'
    },
    {
      id: 'total-cost',
      title: 'Total Cost',
      value: metrics.totalCost,
      change: {
        value: metrics.costToday,
        type: metrics.costToday > 0 ? 'increase' : 'neutral',
        period: 'today'
      },
      icon: '💰',
      color: 'purple',
      format: 'currency'
    },
    {
      id: 'tokens-used',
      title: 'Tokens Used',
      value: metrics.totalTokensUsed,
      change: {
        value: 0, // Would need daily token tracking
        type: 'neutral',
        period: 'today'
      },
      icon: '🔤',
      color: 'indigo',
      format: 'number'
    },
    {
      id: 'active-agents',
      title: 'Active Agents',
      value: metrics.currentActiveAgents,
      change: {
        value: metrics.tasksInProgress,
        type: metrics.tasksInProgress > 0 ? 'increase' : 'neutral',
        period: 'in progress'
      },
      icon: '🤖',
      color: 'cyan',
      format: 'number'
    }
  ] : []

  // Chart data helpers
  const getChartData = useCallback((type: 'agent-distribution' | 'task-trend' | 'cost-breakdown' | 'model-usage'): ChartDataPoint[] => {
    if (!metrics) return []

    switch (type) {
      case 'agent-distribution':
        return Object.entries(metrics.agentTokenUsage).map(([agent, tokens]) => ({
          name: agent,
          value: tokens,
          label: `${tokens.toLocaleString()} tokens`
        }))

      case 'task-trend':
        return metrics.dailyTaskTrend.map(point => ({
          name: point.date,
          value: point.value,
          label: point.label
        }))

      case 'cost-breakdown':
        return Object.entries(metrics.agentCostBreakdown).map(([agent, cost]) => ({
          name: agent,
          value: cost,
          label: `$${cost.toFixed(2)}`
        }))

      case 'model-usage':
        return Object.entries(metrics.modelUsageBreakdown).map(([model, data]) => ({
          name: model,
          value: data.calls,
          label: `${data.calls} calls, $${data.cost.toFixed(2)}`
        }))

      default:
        return []
    }
  }, [metrics])

  const getTimeSeriesData = useCallback((type: 'daily-tasks' | 'weekly-tokens' | 'monthly-usage'): TimeSeriesDataPoint[] => {
    if (!metrics) return []

    switch (type) {
      case 'daily-tasks':
        return metrics.dailyTaskTrend

      case 'weekly-tokens':
        return metrics.weeklyTokenTrend

      case 'monthly-usage':
        return metrics.monthlyUsageTrend

      default:
        return []
    }
  }, [metrics])

  // Format metric helper
  const formatMetric = useCallback((value: number, format: keyof typeof METRIC_FORMATS): string => {
    return METRIC_FORMATS[format](value)
  }, [])

  // Get metric change helper
  const getMetricChange = useCallback((current: number, previous: number) => {
    if (previous === 0) {
      return { value: 0, type: 'neutral' as const }
    }

    const change = ((current - previous) / previous) * 100
    return {
      value: Math.abs(change),
      type: change > 0 ? 'increase' as const : change < 0 ? 'decrease' as const : 'neutral' as const
    }
  }, [])

  return {
    // Data
    metrics,
    insights,
    recommendations,
    dashboardCards,
    isLoading,
    error,
    
    // Filtering
    filter,
    setFilter,
    clearFilter,
    
    // Actions
    refreshAnalytics,
    
    // Chart data helpers
    getChartData,
    getTimeSeriesData,
    
    // Utility
    formatMetric,
    getMetricChange
  }
}
